import time
import threading
from queue import Queue
from copy import deepcopy

from spain_visa_login import user_login
from extension.logger import logger
from tool import get_new_proxy, send_dd_msg, send_wx_msg

from config import headers, url_host
from user_manager import get_users_with_queue_name, save_user_2_redis_queue, move_user_2_queue
from user_manager import spain_error_users, spain_delete_users, del_user_from_redis, spain_user_field, get_user_info
from bypass_login_page import extract_form_data_from_html
from bypass_verify_code import bypass_verify_code_pipeline
from extension.session_manager import create_session
from tool import update_booking_status, BOOK_STATUS

user_queue = Queue()


def user_account_delete(user, session):
    try:
        url_page_delete = url_host + "/CHN/account/DeleteUser"
        res_delete = session.get(url_page_delete, verify=False, timeout=15)

        if res_delete.status_code != 200:
            logger.error(f"#注销#{res_delete.status_code}:{res_delete.text}")
            return False
        # 从html解析验证码链接并且完成验证码校验
        res_verify, res_ = bypass_verify_code_pipeline(session, res_delete.text)
        if not res_verify:
            logger.error(f"#注销#{user['email']}, 验证码校验失败")
            return False

        unregiste_form = extract_form_data_from_html(res_delete.text)
        unregiste_form["captchaId"] = res_verify["captchaId"]
        url_confirm_delte = url_host + "/CHN/Account/DeleteBLSCustomer"
        res_confim = session.post(url_confirm_delte, data=unregiste_form, verify=False, timeout=15)
        if res_confim.status_code == 200 and res_confim.json().get("success"):
            msgs = f"#西班牙用户注销# {user.get('chnname')} | {user['centerCode']} | {user['passportNO']} | {user['visaTypeCode']} | {user['email']}  账号注销成功"
            logger.success(msgs)
            send_wx_msg(msgs)
            send_dd_msg(msgs)
            return True
        else:
            msgs = f"#注销# {user.get('chnname')}:{user['email']}-{user['passportNO']}, 注销失败:{res_confim.text}"
            logger.error(msgs)
            send_dd_msg(msgs)
            return False
    except Exception as e:
        logger.error(f"#注销#流程出错:{user['email']}-{user['passportNO']}:{e.args[0]}")
        return False


# @timmer
def delete_user_after_login(user):
    header = deepcopy(headers)
    cookie_dict = user.get("cookies", None)
    proxy = user.get("proxy", None)
    session = create_session(proxy, cookie_dict, header)

    # 登录状态失效 或者 cookie快要过期   --- 假设cookie过期时间是20分钟
    if not user.get("is_login", False) or int(time.time()) - int(user.get("updateTime", 0)) > 580:
        proxy = get_new_proxy()
        if not proxy:
            # logger.error("无可用代理")
            return False

        proxy_dict = {"http": proxy, "https": proxy}
        session.proxies.update(proxy_dict)

        logger.info(f"##正在登录##{user.get('email')}: {user.get('passportNO')}, update:{user.get('updateTime','')}")
        flag_login, info_login = user_login(user, session)
        if not flag_login:
            if info_login == "deleted":
                del_user_from_redis(user)
                logger.info(f"##用户已注销##{user.get('email')}: {user.get('passportNO')}, update:{user.get('updateTime','')}")
                return None
            user["is_login"] = False
            logger.info(f"##用户登录失败##{user.get('email')}: {user.get('passportNO')}")
        else:
            logger.info(f"##用户登录成功##{user.get('email')}: {user.get('passportNO')}")
            cookie_dict = session.cookies.get_dict()
            user["cookies"] = cookie_dict
            user["proxy"] = proxy_dict["http"]
            user["is_login"] = True
            user["updateTime"] = int(time.time())

        save_user_2_redis_queue(user)
        # 未登录成功
        if not flag_login:
            return False

    delete = user_account_delete(user, session)
    if delete:  # 注销成功
        if user["status"] == "delete_2_registe":  # 真实用户，删除重建
            del_user_from_redis(user)
            user["status"] = "pending"
            user["is_login"] = False
            user["queue_name"] = spain_user_field
            # if get_user_info(user): # 
            save_user_2_redis_queue(user) # 把原队列的用户status改回pending重新注册
        else:
            user["status"] = "deleted"
            move_user_2_queue(user, spain_delete_users)
            logger.info("用户已注销")
            update_booking_status(user, BOOK_STATUS.ACCOUNT_DELETED, "账号已注销")
    else:
        user["is_login"] = delete
        save_user_2_redis_queue(user)
    return delete


def worker_keep_user_login():
    while not user_queue.empty():
        user = user_queue.get()
        delete_user_after_login(user)
        user_queue.task_done()


def brazil_users_delete(thread_count=2):
    logger.info("#注销# 开始注销用户...")
    while True:
        all_users = get_users_with_queue_name(spain_error_users)
        users = list(filter(lambda u: u["status"] in ["canceled", "upadte_error", "update_appointment", "passport_exist", "delete_2_registe", "pending"] and u.get("email", False), all_users))
        users = list(filter(lambda u: u["passportNO"] not in ["*********"], users))
        user_names = [x["chnname"] for x in users]
        # 未登录的用户添加到队列进行登陆
        if len(users) <= 0:
            time.sleep(5)
            continue

        logger.info(f"#注销# 开始注销: {'｜'.join(user_names) }")
        for user in users:
            user["queue_name"] = spain_error_users
            user_queue.put(user)

        threads = []
        for _ in range(thread_count):
            thread = threading.Thread(target=worker_keep_user_login)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()
        time.sleep(5)


if __name__ == "__main__":
    # 获取外部参数
    brazil_users_delete()
