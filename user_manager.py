from RedisClient import RedisClient
import time
import json
from extension.logger import logger
from datetime import datetime
from copy import deepcopy

# ************* 新的阿里云redis
# ************* 旧的用户redis
# ************* 旧的邮箱验证码

redis_client = RedisClient(host="*************")
# email_client = RedisClient(host="*************")
# user_client = RedisClient(host="*************")

# redis 扫号的用户队列
spain_faker_scaning = "spain_users_scaning"  # 扫号的用户队列
spain_user_field = "spainUserDatas"  # 注册好的用户队列
spain_success_users = "spainSuccessUsers"  # 预约成功的用户队列
spain_error_users = "spainDeletedUserDatas"  # 等待注销的用户队列
spain_delete_users = "spainDeleteUsers202506"  # 已经注销的用户队列
spain_slot_cancel_users = "spain_users_slot_cancel"  # 取消预约的用户队列
visa_logs_hash = "visa_logs_hash"  # 放号日志

# redis publish 频道
spain_msg_channel = "spain_appointment_channel"


def save_logs(user, log):
    delete_log = False
    accpet_vip = True if user.get("acceptVIP") == 1 else False  # 是否扫了VIP
    isVIP = user.get("dateVIP")  # 是否扫到了VIP， 没扫到就要把数据删除

    # 没开放VIP 删除信息
    if accpet_vip and not isVIP:
        delete_log = True
    elif len(log["dates"]) == 0:
        delete_log = True

    if user.get("visaTypeCode") and user.get("centerCode"):
        vip_type_number = 1 if user.get("dateVIP") else 2
        field = f"spain-{user['centerCode']}-{user['visaTypeCode']}-{vip_type_number}"
        if delete_log:
            # res = redis_client.hdel(visa_logs_hash, field)
            res = redis_client.hset(visa_logs_hash, field, json.dumps(log, ensure_ascii=False))
        else:
            res = redis_client.hset(visa_logs_hash, field, json.dumps(log, ensure_ascii=False))
        return res
    else:
        logger.error("存log信息失败, 缺失必要信息")
        return None


def get_email_otp(email):
    default_pwd = None
    for _ in range(100):
        # while True:
        # 从redis获取邮箱的默认密码
        default_pwd = redis_client.get(email)
        if default_pwd:
            redis_client.delete(email)
            logger.info(f"邮箱:{email} -- 确认码:{default_pwd}")
            break
        time.sleep(0.2)
    return default_pwd


def delete_email_otp(email):
    try:
        res = redis_client.delete(email)
    except Exception as e:
        logger.error(f"delete_email_otp error {e}")
    return res


def get_user_centers():
    center_str = redis_client.get("spain_center_codes")
    if center_str:
        # print(center_str)
        return json.loads(center_str)
    else:
        user_centers = []
        spain_users = get_users_with_queue_name(spain_user_field)
        for u in spain_users:
            if u["centerCode"] not in user_centers:
                user_centers.append(u["centerCode"])
        return list(set(user_centers))


def get_user_info(user):
    field = f"{user.get('centerCode')}-{user.get('passportNO')}"
    user = redis_client.hget(user["queue_name"], field)
    if user:
        return json.loads(user)
    return None


def hget_redis_user(field, queue_name=spain_faker_scaning):
    user = redis_client.hget(queue_name, field)
    if user:
        return json.loads(user)
    return None


# 保存、更新扫号用户状态
def save_user_2_redis_queue(user_info, check=True):
    # 存注册好的用户信息
    queue_name = user_info.get("queue_name", spain_user_field)
    if user_info.get("passportNO") and user_info.get("centerCode"):
        field = f"{user_info.get('centerCode')}-{user_info.get('passportNO')}"
        # spain_user_field 队列保存前先判断是否存在 已经被移走的不要重复保存
        if check and queue_name == spain_user_field and not redis_client.hget(queue_name, field):
            logger.warning(f"{user_info.get('email')}不在 {spain_user_field} 表中, 更新终止。")
            return
        res = redis_client.hset(queue_name, field, json.dumps(user_info, ensure_ascii=False))
        # msg = "存入成功" if res else "更新成功"
        # logger.debug(f"用户:{user_info.get('email')},resdis信息{msg}")
        return res
    else:
        logger.error(f"{user_info.get('email')}更新redis用户信息失败, 缺失必要信息")
        return None


def move_user_2_queue(user_info, queue_name):
    # 从用户队列删除
    del_user_from_redis(user_info)
    user_info["queue_name"] = queue_name
    # 保存到成功队列
    save_user_2_redis_queue(user_info)


def move_user_2_success_queue(user_info):
    try:
        # 从用户队列删除
        if user_info["queue_name"] == spain_user_field:
            del_user_from_redis(user_info)
            user_info["queue_name"] = spain_success_users
        # 保存到成功队列
        save_user_2_redis_queue(user_info)
    except Exception as e:
        logger.error(f"移动到spainSuccessUsers失败:{user_info}:{e.args[0]}")


def del_user_from_redis(user_info):
    queue_name = user_info.get("queue_name", spain_faker_scaning)
    if user_info.get("centerCode") and user_info.get("passportNO"):
        field = f"{user_info.get('centerCode').upper()}-{user_info.get('passportNO')}"
        res = redis_client.hdel(queue_name, field)
        return res


# @timmer
def get_users_with_queue_name(queue_name=spain_faker_scaning):
    # 存注册好的用户信息
    res = redis_client.hgetall(queue_name)
    return res


def set_user_date_status(user, in_date):
    key_v = f"spain-{user.get('centerCode')}-{user.get('passportNO')}"
    expire_seconds = 60 * 10
    if in_date:
        redis_client.set(key_v, "true", expire_seconds)
    else:
        redis_client.set(key_v, "false", expire_seconds)


def cache_req_token(token, query=False):
    try:
        key_token = "spain-head-req-tokens"
        tokens = json.loads(redis_client.get(key_token))
        if query:
            return tokens
        else:
            tokens.append(token)
            return redis_client.set(key_token, json.dumps(tokens, ensure_ascii=False))
    except:
        pass


def get_user_date_status(user):
    try:
        key_v = f"spain-{user.get('centerCode')}-{user.get('passportNO')}"
        res = redis_client.get(key_v)
        return res == "true"
    except:
        return False

def set_appointment_info(field, msg):
    redis_client.set(field, msg, 60 * 10)

def publish_redis_msg(msg: str, channel=spain_msg_channel):
    redis_client.publish(channel, msg)


def subscribe_redis_msg(call_back, channel=spain_msg_channel):
    redis_client.subscribe(channel, call_back)


def unsubscribe_redis_msg(channel=spain_msg_channel):
    redis_client.unsubscribe(channel)


center_codes = [
    "FUZHOU",
    "GUANGZHOU",
    "BEIJING",
    "HANGZHOU",
    "CHANGSHA",
    "KUNMING",
    "SHANGHAI",
    "SHENYANG",
    "XIAN",
    "JINAN",
    "CHONGQING",
    "SHENZHEN",
    "NANJING",
    "CHENGDU",
]


def print_user(u):
    try:
        time_create = datetime.fromtimestamp(float(u.get("createTime", 0))).strftime("%Y-%m-%d")
        infos = [
            u["centerCode"],
            u.get("visaTypeCode"),
            u.get("passportNO"),
            u["chnname"],
            # time_create,
            # u.get("email", ""),
            u.get("password", ""),
            u.get("status", ""),
            u.get("startDate", ""),
            u.get("endDate", ""),
            "VIP" if u.get("acceptVIP") == 1 else "Normal",
            # u.get("appointment_no", "app_no"),
            u.get("travelDate", ""),
            str(u.get("acceptND")),
            u.get("avatar_image", ""),
        ]
        log_msg = ", ".join(infos)
        print(log_msg)
        return log_msg
    except Exception as e:
        return print(e)


if __name__ == "__main__":
    # center_codes = [
    #     "GUANGZHOU",
    #     "HANGZHOU",
    #     "SHANGHAI",
    #     "XIAN",
    #     "NANJING",
    # ]
    # redis_client.set("spain_center_codes", json.dumps(center_codes))
    # default_centers = get_user_centers()
    # print(default_centers)

    all_faker_users = get_users_with_queue_name()
    # all_spain_users = get_users_with_queue_name("spain_date_test")
    all_faker_users = sorted(all_faker_users, key=lambda x: x["centerCode"])
    # all_spain_users = list(filter(lambda x: x["passportNO"] in ["*********"], all_spain_users))
    for i, u in enumerate(all_faker_users):
        # new_u = deepcopy(u)
        # new_u['queue_name'] = "new_queue"
        # time_create = datetime.fromtimestamp(float(u.get("createTime", 0))).strftime("%Y-%m-%d")
        # print(u.get("centerCode"), u.get("visaTypeCode"), u.get("passportNO"), "VIP" if u.get("acceptVIP") == 1 else "Normal", u["status"], u.get("email"), u.get("password"), u["chnname"])
        # u["status"] = "update_appointment"
        # u["queue_name"] = spain_user_field
        # u["appointment_id"] = data[u.get("passportNO")]  # 下载预约信的ID
        # u["appointment_day"] = "2024-10-31"  # 预约的日期
        # u["appointment_time"] = "09:00-09:15"  # 预约的时间
        # u["pay_time"] = str(time.time())  # 预约成功的时间
        # move_user_2_success_queue(u)
        # if u.get("passportNO") in ["*********"]:
        #     u["status"] = "pending"
        #     u["is_login"] = False
        #     u["queue_name"] = spain_user_field
        #     # 保存到成功队列
        # save_user_2_redis_queue(u, False)
        #     print(u)
        #     u["password"] = "Kq123456@"
        # u["queue_name"] = spain_user_field
        # u["email"] = "<EMAIL>"
        # save_user_2_redis_queue(u)
        # del_user_from_redis(u)
        # print(u.get("centerCode"), u.get("visaTypeCode"), u.get("passportNO"), "VIP" if u.get("acceptVIP") == 1 else "Normal", u["status"], u.get("email"), u.get("password"), u["chnname"])
        print_user(u)
    print("end")
