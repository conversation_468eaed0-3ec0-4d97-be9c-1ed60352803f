import re
from bs4 import BeautifulSoup
from extension.logger import logger
import os
import json


def extract_login_page_info(soup: BeautifulSoup, url="/CHN/account/LoginSubmit"):
    # 从 HTML 中提取所有 CSS 样式
    style_rules = ""
    for style in soup.find_all("style"):
        style_rules += style.string if style.string else ""

    # 从 HTML 中找出对应 ID 的元素、class 和样式
    id_to_class_and_style = {}
    post_data = ""
    el_forms = soup.select("form")
    divs_ = None
    for form in el_forms:
        if url not in form.attrs.get("action"):
            continue
        url = form.attrs.get("action")
        divs_ = form.find_all("div")
        post_data = {x.get("name"): x.get("value") for x in form.select("input")}

    for el_div in divs_:
        el_label = el_div.find("label")
        if not el_label:
            continue

        label_id = el_label.attrs.get("for")
        # print(el_id, label_id)
        if not label_id:
            continue
        classes = " ".join(el_div.get("class", [])).split()
        # el_type = el.attrs.get("type")
        styles = {}
        for cls_ in classes:
            pattern = re.compile(rf"\.{re.escape(cls_)}\s*\{{(.*?)\}}", re.DOTALL)
            matches = pattern.findall(style_rules)

            # 合并样式
            for match in matches:
                for declaration in match.split(";"):
                    if declaration.strip():
                        property_name, _, value = declaration.partition(":")
                        styles[property_name.strip()] = value.strip()

        # 保存class和解析出来的styles
        id_to_class_and_style[label_id] = {
            "classes": classes,
            "styles": styles,  # 初始化为空，稍后将填充样式
            "id": label_id,
            "text": el_label.text,
        }

    # 判断元素的显示状态
    visible_ids = set()
    for element_id, details in id_to_class_and_style.items():
        styles = details["styles"]
        # 根据最终样式判断 display 属性
        display = styles.get("display", "inline")
        # style 后生效
        if "inline" in display or "block" in display:
            visible_ids.add((element_id, details["text"]))

    visible_ids = list(visible_ids)[0]
    res_ids = {x: "" for x in id_to_class_and_style.keys()}
    return visible_ids[0], post_data, res_ids, url


@logger.catch
def bypass_login_page(html_doc, user_name):
    try:
        soup = BeautifulSoup(html_doc, "html.parser")
        e_id, post_data, all_ids, url = extract_login_page_info(soup)
        all_ids[e_id] = user_name
        post_data.update(all_ids)
        post_data["ResponseData"] = json.dumps(all_ids)
        return True, post_data, url
    except Exception as e:
        logger.error(f"登录页信息解析错误：{e}")
        return False, {}, ""


@logger.catch
def extract_form_data_from_html(html_doc):
    try:
        soup = BeautifulSoup(html_doc, "html.parser")
        form_data = {_.get("name"): _.get("value") for _ in soup.select("input")}
        form_data["X-Requested-With"] = "XMLHttpRequest"
        return form_data
    except Exception:
        return None
    
@logger.catch
def extract_editId_locationId_visaTypeId(html_string):
    pattern = r"ManageApplicant\('([^']*)','([^']*)','([^']*)'\)"
    matches = re.findall(pattern, html_string, re.DOTALL)
    # 输出结果
    for match in matches:
        editId, visaTypeId, locationId = match
        if len(editId):
            return editId, visaTypeId, locationId
    return None, None, None
 # 旧版 只有一个该方法的调用
    if match:
        editId = match.group(1)
        visaTypeId = match.group(2)
        locationId = match.group(3)
        return editId, visaTypeId, locationId
    else:
       return None, None, None
    
    
    match = re.search(pattern, html_string, re.DOTALL)
    if match:
        editId = match.group(1)
        visaTypeId = match.group(2)
        locationId = match.group(3)
        return editId, visaTypeId, locationId
    else:
       return None, None, None


@logger.catch
def extract_manager_uri(html_string):
    uri_pattern =r"win\.iframeOpenUrl\s*=\s*'([^']*)'"
    matches =re.findall(uri_pattern, html_string, re.DOTALL)
    if matches:
        for uri_link in matches:
            if "/CHN/appointmentdata/ManageApplicant" in uri_link:
                return uri_link
    return None


if __name__ == "__main__":
    files_path = [os.path.join(os.path.dirname(__file__), "htmls", "xian.html")]
    for file_path in files_path:
        with open(file_path, "r", encoding="utf-8") as fs:
            doc = fs.read()
        res = extract_editId_locationId_visaTypeId(doc)
        print("\n", file_path, ":", res)
