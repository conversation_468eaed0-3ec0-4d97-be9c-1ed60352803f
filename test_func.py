from tool import str_2_timestamp
import datetime
import time

open_dates = [
    "2025-07-03",
    "2025-07-04",
    "2025-07-07",
    "2025-07-08",
    "2025-07-09",
    "2025-07-10",
    "2025-07-11",
    "2025-07-14",
    "2025-07-15",
    "2025-07-16",
    "2025-07-17",
    "2025-07-18",
    "2025-07-21",
    "2025-07-22",
    "2025-07-23",
    "2025-07-24",
    "2025-07-25",
    "2025-07-28",
    "2025-07-29",
    "2025-07-30",
    "2025-07-31",
]


def pick_available_day(acceptND=1):
    start_date = "2025-07-03"
    end_date = "2025-07-28"
    stamp_start = str_2_timestamp(start_date)
    stamp_end = str_2_timestamp(end_date)
    # 筛选出可预约日期
    available_dates = []
    # open_dates = list(filter(lambda x: x.get("SingleSlotAvailable", False), all_dates))
    for date_item in open_dates:
        item_stamp = str_2_timestamp(date_item)
        if stamp_start <= item_stamp and item_stamp <= stamp_end:
            if acceptND == 2:
                # if item_stamp - datetime.datetime.now().timestamp() <= 3600 * 24:
                if item_stamp - time.time() > 3600 * 24:
                    available_dates.append(date_item)
            else:
                available_dates.append(date_item)

    print(available_dates)
    return available_dates


# pick_available_day(2)

from PIL import Image
import io

def compress_image(image_data, max_size_kb=150, quality=85):
    """
    压缩图片到指定大小以内，PNG转JPG
    
    Args:
        image_data: 图片二进制数据
        max_size_kb: 最大文件大小(KB)，默认150KB
        quality: JPG质量(1-100)，默认85
    
    Returns:
        bytes: 压缩后的图片二进制数据
    """
    try:
        # 打开图片
        image = Image.open(io.BytesIO(image_data))
        
        # 如果是RGBA模式(PNG透明)，转换为RGB
        if image.mode in ('RGBA', 'LA', 'P'):
            # 创建白色背景
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 获取原始尺寸
        original_width, original_height = image.size
        max_size_bytes = max_size_kb * 1024
        
        # 初始质量设置
        current_quality = quality
        
        # 如果原图就很小，直接返回
        output = io.BytesIO()
        image.save(output, format='JPEG', quality=current_quality, optimize=True)
        
        if output.tell() <= max_size_bytes:
            return output.getvalue()
        
        # 需要压缩 - 先尝试降低质量
        while current_quality > 20 and output.tell() > max_size_bytes:
            output = io.BytesIO()
            current_quality -= 10
            image.save(output, format='JPEG', quality=current_quality, optimize=True)
        
        # 如果降低质量还不够，缩小尺寸
        scale_factor = 0.9
        while output.tell() > max_size_bytes and scale_factor > 0.3:
            new_width = int(original_width * scale_factor)
            new_height = int(original_height * scale_factor)
            
            # 保持宽高比缩放
            resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            output = io.BytesIO()
            resized_image.save(output, format='JPEG', quality=current_quality, optimize=True)
            
            scale_factor -= 0.1
        
        return output.getvalue()
        
    except Exception as e:
        raise Exception(f"图片压缩失败: {str(e)}")


def compress_image_advanced(image_data, max_size_kb=150):
    """
    高级图片压缩方法，自适应质量和尺寸
    
    Args:
        image_data: 图片二进制数据
        max_size_kb: 最大文件大小(KB)
    
    Returns:
        bytes: 压缩后的图片二进制数据
    """
    try:
        image = Image.open(io.BytesIO(image_data))
        
        # 处理透明度
        if image.mode in ('RGBA', 'LA', 'P'):
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            if image.mode == 'RGBA':
                background.paste(image, mask=image.split()[-1])
            else:
                background.paste(image)
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')
        
        max_size_bytes = max_size_kb * 1024
        original_width, original_height = image.size
        
        # 智能压缩策略
        def try_compress(img, quality, width=None, height=None):
            if width and height:
                img = img.resize((width, height), Image.Resampling.LANCZOS)
            
            output = io.BytesIO()
            img.save(output, format='JPEG', quality=quality, optimize=True)
            return output.getvalue(), output.tell()
        
        # 二分查找最优质量
        def find_optimal_quality(img, target_size, width=None, height=None):
            low, high = 10, 95
            best_data = None
            
            while low <= high:
                mid = (low + high) // 2
                data, size = try_compress(img, mid, width, height)
                
                if size <= target_size:
                    best_data = data
                    low = mid + 1
                else:
                    high = mid - 1
            
            return best_data
        
        # 先尝试原尺寸
        result = find_optimal_quality(image, max_size_bytes)
        if result:
            return result
        
        # 尝试不同的缩放比例
        for scale in [0.9, 0.8, 0.7, 0.6, 0.5, 0.4]:
            new_width = int(original_width * scale)
            new_height = int(original_height * scale)
            
            result = find_optimal_quality(image, max_size_bytes, new_width, new_height)
            if result:
                return result
        
        # 最后兜底：强制压缩到最小
        final_width = int(original_width * 0.3)
        final_height = int(original_height * 0.3)
        data, _ = try_compress(image, 10, final_width, final_height)
        return data
        
    except Exception as e:
        print(f"高级图片压缩失败: {str(e)}")
        return image_data


from main_spain_users_registe import verify_user_head_img
import requests
# 使用示例
if __name__ == "__main__":
    verify_user_head_img({
            "birthday": "1982/05/07",
            "chnname": "雷娜",
            "endDate": "2025-08-31",
            "expiredDT": "2033/02/21",
            "gender": "男",
            "name": "YONG",
            "passportNO": "*********",
            "phone": "***********",
            "startDate": "2025-08-15",
            "visaTypeCode": "Business",
            "xing": "YANG",
            "avatar_image": "avatar_2087c4f8619f42bb9adef8beedf99a33.jpg",
        }, requests.session())
