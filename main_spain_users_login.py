# import requests
import sys
import time
import threading
import json
from queue import Queue
from copy import deepcopy
from datetime import datetime
from curl_cffi import requests
from spain_visa_login import user_login
from extension.logger import logger
from tool import get_new_proxy, get_random_user_agent, str_2_timestamp
from config import headers
from user_manager import save_user_2_redis_queue, get_users_with_queue_name, subscribe_redis_msg, spain_user_field
from user_manager import get_user_info, get_user_date_status, set_user_date_status

user_queue = Queue()
# spain_user_field = "spainUserDatas"


# @timmer
def keep_user_is_login(user):
    user = get_user_info(user)  # 刷新下redis的用户信息
    if not user:
        return
    if get_user_date_status(user):  # 正在预约的不能重新登录
        return

    header = deepcopy(headers)
    header["user-agent"] = get_random_user_agent()
    session = requests.Session(impersonate="chrome131")
    session.headers = header

    if not user.get("is_login", False) or int(time.time()) - int(user.get("updateTime", 0)) > 600:
        proxy = get_new_proxy(faker=False)
        if not proxy:
            return
        proxy_dict = {"http": proxy, "https": proxy}
        session.proxies.update(proxy_dict)

        logger.debug(f"##正在登录##{user.get('email')}: {user.get('passportNO')}, update:{user.get('updateTime','')}")

        flag_login, info_login = user_login(user, session)
        if not flag_login:
            user = get_user_info(user)
            user["is_login"] = False
            logger.error(f"##用户登录失败## {user.get('chnname')} - {user.get('centerCode')} - {user.get('visaTypeCode')} - {user.get('email')} - {user.get('passportNO')} -{user.get('startDate')} - {user.get('endDate')}")
        else:
            logger.success(f"##用户登录成功## {user.get('chnname')} - {user.get('centerCode')} - {user.get('visaTypeCode')} - {user.get('email')} - {user.get('passportNO')} -{user.get('startDate')} - {user.get('endDate')}")
            cookie_dict = session.cookies.get_dict()
            # password = info_login.get('password')
            user = get_user_info(user)
            # user['password'] = password
            user["dest"] = "0cf9da11-cb76-4566-81e5-8628d5488e3c"  # 目的地
            user["cookies"] = cookie_dict
            user["proxy"] = proxy_dict["http"]
            user["is_login"] = True
            user["updateTime"] = int(time.time())
        # 失败成功都要更新到redis
        set_user_date_status(user, False)
        save_user_2_redis_queue(user)
    else:
        pass
        # logger.debug(f"##保持登录##user:{user.get('email')}, login:{user.get('is_login')}, update:{user.get('updateTime')}")
    return user, session


def worker_keep_user_login():
    while not user_queue.empty():
        user = user_queue.get()
        keep_user_is_login(user)
        user_queue.task_done()
        time.sleep(0.5)


def spain_users_login(thread_count=10):
    logger.info("##预约# 开始登录用户...")
    while True:
        all_users = get_users_with_queue_name(spain_user_field)
        users = list(filter(lambda u: u.get("status") == "update_appointment", all_users))
        # 未登录的用户添加到队列进行登陆
        if len(users) <= 0:
            time.sleep(0.5)
            continue

        for user in users:
            user_queue.put(user)

        threads = []
        for _ in range(thread_count):
            thread = threading.Thread(target=worker_keep_user_login, daemon=True)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        time.sleep(0.5)


if __name__ == "__main__":
    spain_users_login()
