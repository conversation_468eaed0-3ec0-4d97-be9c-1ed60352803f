from loguru import logger
import os
import sys

# 配置日志文件路径和保留策略
log_folder = "logs"
if not os.path.exists(log_folder):
    os.makedirs(log_folder)

log_path_all = os.path.join(log_folder, "{time:YYYY-MM-DD}.log")
log_path_error = os.path.join(log_folder, "error.log")

logger.remove()
# 日志简单配置
# 具体其他配置 可自行参考 https://github.com/Delgan/loguru
logger.add(sys.stderr, level="INFO")
logger.add(log_path_all, rotation="00:01", retention="30 days", enqueue=True, level="INFO")
logger.add(log_path_error, rotation="20:00", retention="10 days", enqueue=True, level="ERROR")
